"""
用户相关API接口
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime
import logging

from database import get_db_session
from models import User, InviteCode
from pydantic import BaseModel, Field
from utils.auth import hash_password, authenticate_user, create_user_token
from middleware.auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()


class UserRegisterRequest(BaseModel):
    """用户注册请求模型"""
    username: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="用户名，3-50个字符",
        example="user"
    )
    alipay: Optional[str] = Field(
        None,
        description="支付宝结款手机号（可选）",
        example="13800138000"
    )
    password: str = Field(
        ...,
        min_length=6,
        max_length=128,
        description="密码，至少6个字符",
        example="password"
    )
    invite_code: Optional[str] = Field(
        None,
        description="邀请码（可选），用于推荐返佣",
        example=""
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "username": "user",
                "alipay": "13800138000",
                "password": "password",
                "invite_code": ""
            }
        }
    }


class UserLoginRequest(BaseModel):
    """用户登录请求模型"""
    username: str = Field(
        ...,
        description="用户名",
        example="user"
    )
    password: str = Field(
        ...,
        description="密码",
        example="password"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "username": "user",
                "password": "password"
            }
        }
    }


class UserInfoResponse(BaseModel):
    """用户信息响应模型"""
    id: int = Field(..., description="用户ID", example=1)
    username: str = Field(..., description="用户名", example="user")
    alipay: Optional[str] = Field(None, description="支付宝结款手机号", example="13800138000")
    status: str = Field(..., description="用户状态", example="active")
    referrer_id: Optional[int] = Field(None, description="推荐人ID", example=2)
    created_at: datetime = Field(..., description="注册时间", example="2024-01-01T12:00:00Z")

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": 1,
                "username": "user",
                "alipay": "13800138000",
                "status": "active",
                "referrer_id": 2,
                "created_at": "2024-01-01T12:00:00Z"
            }
        }
    }


class UserRegisterResponse(BaseModel):
    """用户注册响应模型"""
    success: bool = Field(..., description="注册是否成功", example=True)
    message: str = Field(..., description="响应消息", example="注册成功")
    user_id: Optional[int] = Field(None, description="新用户ID", example=1)

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "message": "注册成功",
                "user_id": 1
            }
        }
    }


class UserLoginResponse(BaseModel):
    """用户登录响应模型"""
    success: bool = Field(..., description="登录是否成功", example=True)
    message: str = Field(..., description="响应消息", example="登录成功")
    user_id: Optional[int] = Field(None, description="用户ID", example=1)
    token: Optional[str] = Field(
        None,
        description="JWT访问令牌",
        example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "message": "登录成功",
                "user_id": 1,
                "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }
    }


@router.post(
    "/register",
    response_model=UserRegisterResponse,
    summary="用户注册",
    description="创建新用户账户，支持邀请码推荐机制",
    responses={
        200: {
            "description": "注册成功",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "注册成功",
                        "user_id": 1
                    }
                }
            }
        },
        400: {
            "description": "注册失败 - 用户名已存在或邀请码无效",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": "用户名已存在"
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": "注册失败，请稍后重试"
                    }
                }
            }
        }
    }
)
async def register_user(
    request: UserRegisterRequest,
    db: Session = Depends(get_db_session)
):
    """
    ## 用户注册接口

    创建新用户账户，支持通过邀请码建立推荐关系。

    ### 功能特性
    - ✅ 用户名唯一性验证
    - ✅ 密码安全加密存储
    - ✅ 邀请码推荐机制
    - ✅ 自动建立推荐关系

    ### 注册流程
    1. 验证用户名是否已存在
    2. 验证邀请码有效性（如果提供）
    3. 创建用户账户
    4. 建立推荐关系（如果使用邀请码）
    5. 标记邀请码为已使用

    ### 注意事项
    - 用户名必须唯一，3-50个字符
    - 密码至少6个字符
    - 支付宝手机号可选，用于佣金结算
    - 邀请码可选，用于推荐返佣
    - 推荐关系一旦建立不可更改
    """
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == request.username).first()
        if existing_user:
            return UserRegisterResponse(
                success=False,
                message="用户名已存在"
            )
        
        # 检查支付宝手机号是否已存在（如果提供了手机号）
        if request.alipay:
            existing_alipay = db.query(User).filter(User.alipay == request.alipay).first()
            if existing_alipay:
                return UserRegisterResponse(
                    success=False,
                    message="该支付宝手机号已被使用"
                )
        
        # 处理邀请码
        referrer_id = None
        if request.invite_code:
            invite_code = db.query(InviteCode).filter(
                InviteCode.code == request.invite_code,
                InviteCode.used_by.is_(None)  # 检查邀请码是否未被使用
            ).first()
            if not invite_code:
                return UserRegisterResponse(
                    success=False,
                    message="邀请码无效或已失效"
                )
            referrer_id = invite_code.user_id
        
        # 创建新用户，使用bcrypt加密密码
        password_hash = hash_password(request.password)
        new_user = User(
            username=request.username,
            alipay=request.alipay,
            password_hash=password_hash,
            referrer_id=referrer_id
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 如果使用了邀请码，标记为已使用
        if request.invite_code and invite_code:
            invite_code.used_by = new_user.id
            invite_code.used_at = datetime.now()
            db.commit()
        
        return UserRegisterResponse(
            success=True,
            message="注册成功",
            user_id=new_user.id
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户注册失败: {e}")
        return UserRegisterResponse(
            success=False,
            message="注册失败，请稍后重试"
        )


@router.post(
    "/login",
    response_model=UserLoginResponse,
    summary="用户登录",
    description="用户身份验证，返回JWT访问令牌",
    responses={
        200: {
            "description": "登录成功",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "登录成功",
                        "user_id": 1,
                        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    }
                }
            }
        },
        401: {
            "description": "认证失败 - 用户名或密码错误",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": "用户名或密码错误"
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": "登录失败，请稍后重试"
                    }
                }
            }
        }
    }
)
async def login_user(
    request: UserLoginRequest,
    db: Session = Depends(get_db_session)
):
    """
    ## 用户登录接口

    验证用户身份并返回JWT访问令牌，用于后续API调用认证。

    ### 功能特性
    - ✅ 安全的密码验证
    - ✅ JWT令牌生成
    - ✅ 用户状态检查

    ### 使用说明
    1. 提供用户名和密码
    2. 系统验证身份信息
    3. 返回JWT访问令牌
    4. 在后续请求中使用令牌进行认证

    ### Token使用方法
    在请求头中添加：
    ```
    Authorization: Bearer <返回的token>
    ```

    ### 注意事项
    - Token有效期为30分钟（可配置）
    - Token过期后需要重新登录
    - 请妥善保管Token，避免泄露
    """
    try:
        # 验证用户名和密码
        user = authenticate_user(request.username, request.password, db)
        if not user:
            return UserLoginResponse(
                success=False,
                message="用户名或密码错误"
            )

        # 生成JWT token
        token = create_user_token(user)

        return UserLoginResponse(
            success=True,
            message="登录成功",
            user_id=user.id,
            token=token
        )
        
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        return UserLoginResponse(
            success=False,
            message="登录失败，请稍后重试"
        )


@router.get("/info/{user_id}", response_model=UserInfoResponse)
async def get_user_info(
    user_id: int,
    db: Session = Depends(get_db_session)
):
    """
    获取用户信息接口（公开接口，不需要认证）

    Args:
        user_id: 用户ID
        db: 数据库会话

    Returns:
        UserInfoResponse: 用户信息
    """
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        return UserInfoResponse(
            id=user.id,
            username=user.username,
            alipay=user.alipay,
            status=user.status.value,
            referrer_id=user.referrer_id,
            created_at=user.created_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户{user_id}信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.get("/me", response_model=UserInfoResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息接口（需要认证）

    Args:
        current_user: 当前认证用户

    Returns:
        UserInfoResponse: 当前用户信息
    """
    return UserInfoResponse(
        id=current_user.id,
        username=current_user.username,
        alipay=current_user.alipay,
        status=current_user.status.value,
        referrer_id=current_user.referrer_id,
        created_at=current_user.created_at
    )
